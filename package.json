{"name": "popof<PERSON>i", "version": "1.0.13.20250722032640.4bb68e3", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev --mode dev", "test": "electron-vite dev --mode test", "prod": "electron-vite dev --mode prod", "build": "electron-vite build", "prebuild": "node scripts/increment-version.js", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:win:test": "npm run build -- --mode test && electron-builder --win --config electron-builder.test.yml", "build:win:prod": "npm run build -- --mode prod && electron-builder --win --config electron-builder.yml", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@electron/rebuild": "^4.0.1", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@nut-tree-fork/nut-js": "^4.2.6", "@nut-tree-fork/provider-interfaces": "^4.2.6", "@zumer/snapdom": "^1.7.1", "adapter-draft": "^2.0.0", "animate.css": "^4.1.1", "animejs": "^4.0.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "dom-to-image": "^2.6.0", "electron-updater": "^6.3.9", "element-plus": "^2.9.11", "express": "^4.18.2", "gsap": "^3.12.7", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "node-machine-id": "^1.1.12", "pinia": "^3.0.3", "qrcode": "^1.5.4", "three": "^0.174.0", "vite-plugin-mkcert": "^1.17.8", "vue-router": "^4.5.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "electron": "^34.2.0", "electron-builder": "^25.1.8", "electron-vite": "^3.0.0", "eslint": "^9.20.1", "eslint-plugin-vue": "^9.32.0", "less": "^4.3.0", "prettier": "^3.5.1", "typescript": "^5.7.3", "vite": "^6.1.0", "vue": "^3.5.13", "vue-tsc": "^2.2.2"}}